<script setup lang="ts">
import { ref, reactive, getCurrentInstance, inject, onMounted, computed, onUnmounted } from 'vue'
import * as echarts from "echarts"

const parent = getCurrentInstance()
const activeName = inject('activeName')
const loading = ref(false)

// 图表引用
const aleftboxtmiddRef = ref(null)
const aleftboxtbottRef = ref(null)
const amiddboxtbott1Ref = ref(null)
const amiddboxtbott2Ref = ref(null)
const arightboxbottRef = ref(null)

// 数据统计
const todayStats = reactive({
  disputes: 54,
  resolved: 54,
  unresolved: 4,
  processing: 4,
  region: '甘孜',
  date: '2018-06-14'
})

// 警力分析数据
const policeAnalysisList = ref([
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  },
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  },
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  },
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  },
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  },
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  },
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  },
  {
    department: '康定市公安局',
    description: '村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。',
    date: '2018-06-22'
  }
])

// 导航菜单
const leftMenus = ref([
  { name: '警情警力', active: true },
  { name: '实有人口', active: false },
  { name: '流动人口', active: false },
  { name: '实名制', active: false }
])

const rightMenus = ref([
  { name: '返回' },
  { name: '分析报告' },
  { name: '交通' },
  { name: '舆情' }
])

// 菜单点击事件
const handleLeftMenuClick = (index: number) => {
  leftMenus.value.forEach((item, i) => {
    item.active = i === index
  })
}

const handleRightMenuClick = (menuName: string) => {
  console.log('点击了:', menuName)
  // 这里可以添加路由跳转或其他逻辑
}

// 初始化图表
const initCharts = () => {
  // 矛盾纠纷地区统计 - 饼图
  const regionChart = echarts.init(aleftboxtmiddRef.value)
  const regionOption = {
    color: ['#76c4bf', '#e5ffc7', '#508097', '#4d72d9'],
    backgroundColor: 'rgba(1,202,217,.2)',
    grid: {
      left: 10,
      right: 40,
      top: 20,
      bottom: 0,
      containLabel: true
    },
    calculable: true,
    series: [{
      name: '面积模式',
      type: 'pie',
      radius: [5, 60],
      center: ['50%', '55%'],
      roseType: 'area',
      data: [
        { value: 10, name: '康定市' },
        { value: 5, name: '丹巴县' },
        { value: 15, name: '甘孜县' },
        { value: 25, name: '理塘县' }
      ]
    }]
  }
  regionChart.setOption(regionOption)

  // 矛盾纠纷类型统计 - 柱状图
  const typeChart = echarts.init(aleftboxtbottRef.value)
  const typeOption = {
    color: ['#7ecef4'],
    backgroundColor: 'rgba(1,202,217,.2)',
    grid: {
      left: 20,
      right: 20,
      top: 13,
      bottom: 6,
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: 'rgba(255,255,255,.2)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255,255,255,0)'
        }
      },
      axisLabel: {
        color: "rgba(255,255,255,0)"
      },
      boundaryGap: [0, 0.01]
    },
    yAxis: {
      type: 'category',
      axisLine: {
        lineStyle: {
          color: 'rgba(255,255,255,.5)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255,255,255,.1)'
        }
      },
      axisLabel: {
        color: "rgba(255,255,255,.5)"
      },
      data: ['未调节', '调节中', '已调节']
    },
    series: [{
      name: '2018年',
      type: 'bar',
      barWidth: 30,
      itemStyle: {
        normal: {
          color: new echarts.graphic.LinearGradient(
            1, 0, 0, 1,
            [
              { offset: 0, color: 'rgba(230,253,139,.7)' },
              { offset: 1, color: 'rgba(41,220,205,.7)' }
            ]
          )
        }
      },
      data: [18203, 23489, 29034]
    }]
  }
  typeChart.setOption(typeOption)

  // 环比分析散点图
  if (amiddboxtbott1Ref.value) {
    const trendChart = echarts.init(amiddboxtbott1Ref.value)
    const data = [
      [[28604,77,17099,'Australia',1990],[31163,77.4,2440,'Canada',1990],[1516,68,1605773,'China',1990]],
      [[44056,81.8,23973,'Australia',2015],[43294,81.7,35927,'Canada',2015],[13334,76.9,1376043,'China',2015]]
    ]

    const trendOption = {
      backgroundColor: 'rgba(1,202,217,.2)',
      grid: {
        left: 40,
        right: 40,
        top: 50,
        bottom: 40
      },
      title: {
        top: 5,
        left: 20,
        textStyle: {
          fontSize: 10,
          color: 'rgba(255,255,255,.6)'
        },
        text: '环比类型：日环比'
      },
      legend: {
        right: 10,
        top: 5,
        textStyle: {
          fontSize: 10,
          color: 'rgba(255,255,255,.6)'
        },
        data: ['1990', '2015']
      },
      xAxis: {
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.2)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        },
        axisLabel: {
          color: "rgba(255,255,255,.7)"
        }
      },
      yAxis: {
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.2)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        },
        axisLabel: {
          color: "rgba(255,255,255,.7)"
        },
        scale: true
      },
      series: [{
        name: '1990',
        data: data[0],
        type: 'scatter',
        symbolSize: function (data) {
          return Math.sqrt(data[2]) / 5e2;
        },
        itemStyle: {
          normal: {
            shadowBlur: 10,
            shadowColor: 'rgba(120, 36, 50, 0.5)',
            shadowOffsetY: 5,
            color: new echarts.graphic.RadialGradient(0.4, 0.3, 1, [{
              offset: 0,
              color: 'rgb(251, 118, 123)'
            }, {
              offset: 1,
              color: 'rgb(204, 46, 72)'
            }])
          }
        }
      }, {
        name: '2015',
        data: data[1],
        type: 'scatter',
        symbolSize: function (data) {
          return Math.sqrt(data[2]) / 5e2;
        },
        itemStyle: {
          normal: {
            shadowBlur: 10,
            shadowColor: 'rgba(25, 100, 150, 0.5)',
            shadowOffsetY: 5,
            color: new echarts.graphic.RadialGradient(0.4, 0.3, 1, [{
              offset: 0,
              color: 'rgb(129, 227, 238)'
            }, {
              offset: 1,
              color: 'rgb(25, 183, 207)'
            }])
          }
        }
      }]
    }
    trendChart.setOption(trendOption)
  }

  // 案件统计图表
  if (amiddboxtbott2Ref.value) {
    const caseChart = echarts.init(amiddboxtbott2Ref.value)
    const caseOption = {
      backgroundColor: 'rgba(1,202,217,.2)',
      grid: {
        left: 60,
        right: 60,
        top: 50,
        bottom: 40
      },
      legend: {
        top: 10,
        textStyle: {
          fontSize: 10,
          color: 'rgba(255,255,255,.7)'
        },
        data: ['2017年', '2018年', '同比增速']
      },
      xAxis: [{
        type: 'category',
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.2)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        },
        axisLabel: {
          color: "rgba(255,255,255,.7)"
        },
        data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
        axisPointer: {
          type: 'shadow'
        }
      }],
      yAxis: [{
        type: 'value',
        name: '',
        min: 0,
        max: 250,
        interval: 50,
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.01)'
          }
        },
        axisLabel: {
          formatter: '{value} ml'
        }
      }, {
        type: 'value',
        name: '',
        max: 25,
        interval: 5,
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.3)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        },
        axisLabel: {
          formatter: '{value} °C'
        }
      }],
      series: [{
        name: '2017年',
        type: 'bar',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [
                { offset: 0, color: '#b266ff' },
                { offset: 1, color: '#121b87' }
              ]
            )
          }
        },
        data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3]
      }, {
        name: '2018年',
        type: 'bar',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [
                { offset: 0, color: '#00f0ff' },
                { offset: 1, color: '#032a72' }
              ]
            )
          }
        },
        data: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3]
      }, {
        name: '同比增速',
        type: 'line',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0, 0, 0, 1,
              [
                { offset: 0, color: '#fffb34' },
                { offset: 1, color: '#fffb34' }
              ]
            )
          }
        },
        yAxisIndex: 1,
        data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
      }]
    }
    caseChart.setOption(caseOption)
  }

  // 七日数据分析图表
  if (arightboxbottRef.value) {
    const sevenDayChart = echarts.init(arightboxbottRef.value)
    const sevenDayOption = {
      color: ['#7de494', '#7fd7b1', '#5578cf', '#5ebbeb', '#d16ad8', '#f8e19a', '#00b7ee', '#81dabe', '#5fc5ce'],
      backgroundColor: 'rgba(1,202,217,.2)',
      grid: {
        left: '5%',
        right: '8%',
        bottom: '7%',
        top: '8%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.2)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        },
        axisLabel: {
          color: "rgba(255,255,255,.7)"
        },
        data: ['6-08', '6-09', '6-10', '6-11', '6-12', '6-13', '6-14']
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.2)'
          }
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.1)'
          }
        },
        axisLabel: {
          color: "rgba(255,255,255,.7)"
        }
      },
      series: [{
        name: '2014年',
        type: 'line',
        stack: '总量',
        areaStyle: { normal: {} },
        data: [120, 132, 101, 134, 90, 230, 210]
      }]
    }
    sevenDayChart.setOption(sevenDayOption)
  }
}

onMounted(() => {
  initCharts()
})

onUnmounted(() => {
  // 清理图表实例
})
</script>

<template>
  <div class="analysis-container">
    <!-- 顶部导航 -->
    <div class="top-nav">
      <div class="nav-left">
        <ul>
          <li 
            v-for="(menu, index) in leftMenus" 
            :key="index"
            :class="{ active: menu.active }"
            @click="handleLeftMenuClick(index)"
          >
            <a href="#">{{ menu.name }}</a>
          </li>
        </ul>
      </div>
      <h1 class="title">矛盾纠纷分析</h1>
      <div class="nav-right">
        <ul>
          <li 
            v-for="(menu, index) in rightMenus" 
            :key="index"
            @click="handleRightMenuClick(menu.name)"
          >
            <a href="#">{{ menu.name }}</a>
          </li>
        </ul>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 今日数据统计 -->
        <div class="stats-box">
          <h2 class="section-title">今日矛盾纠纷数据统计</h2>
          <div class="stats-header">
            <p class="region">地区：{{ todayStats.region }}</p>
            <p class="date">{{ todayStats.date }}</p>
          </div>
          <div class="stats-numbers">
            <div class="stat-item">
              <p>矛盾纠纷</p>
              <h3 class="number-orange">{{ todayStats.disputes }}</h3>
              <h4 class="trend">环比<img src="./003/img/iconup.png" height="16" />2%</h4>
            </div>
            <div class="stat-item">
              <p>已调节</p>
              <h3 class="number-blue">{{ todayStats.resolved }}</h3>
              <h4 class="trend">环比<img src="./003/img/icondown.png" height="16" />3%</h4>
            </div>
            <div class="stat-item">
              <p>未调节</p>
              <h3 class="number-yellow">{{ todayStats.unresolved }}</h3>
              <h4 class="trend">环比<img src="./003/img/icondown.png" height="16" />3%</h4>
            </div>
            <div class="stat-item">
              <p>处理中</p>
              <h3 class="number-green">{{ todayStats.processing }}</h3>
              <h4 class="trend">环比<img src="./003/img/icondown.png" height="16" />3%</h4>
            </div>
          </div>
        </div>

        <!-- 地区统计图表 -->
        <div class="chart-box">
          <h2 class="section-title">矛盾纠纷地区统计</h2>
          <div class="chart-header">
            <p>状态：已调节</p>
            <p>时间段：2018-06-10 至 2018-06-14</p>
          </div>
          <div ref="aleftboxtmiddRef" class="chart-content"></div>
        </div>

        <!-- 类型统计图表 -->
        <div class="chart-box">
          <h2 class="section-title">矛盾纠纷类型统计</h2>
          <div class="chart-header">
            <p>状态：已调节</p>
            <p>时间段：2018-06-10 至 2018-06-14</p>
          </div>
          <div ref="aleftboxtbottRef" class="chart-content"></div>
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="middle-section">
        <!-- 实时监控统计 -->
        <div class="monitor-box">
          <h2 class="section-title">实时监控统计</h2>
          <div class="monitor-map">
            <span class="camera-icon" style="top:34%;left:32%"></span>
            <span class="camera-icon" style="top:10%;left:10%"></span>
            <span class="camera-icon" style="top:5%;left:40%"></span>
            <span class="camera-icon" style="top:10%;left:50%"></span>
            <span class="camera-icon" style="top:30%;left:75%"></span>
            <span class="camera-icon" style="top:5%;left:92%"></span>
            <span class="camera-icon" style="top:40%;left:83%"></span>
          </div>
        </div>

        <!-- 底部图表区域 -->
        <div class="bottom-charts">
          <div class="chart-left">
            <h2 class="section-title">矛盾纠纷环比分析</h2>
            <div ref="amiddboxtbott1Ref" class="chart-content"></div>
          </div>
          <div class="chart-right">
            <h2 class="section-title">案件统计</h2>
            <div ref="amiddboxtbott2Ref" class="chart-content"></div>
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 警力分析 -->
        <div class="police-analysis">
          <h2 class="section-title">警力分析</h2>
          <div class="chart-header">
            <p>状态：已调节</p>
            <p>时间段：2018-06-10</p>
          </div>
          <div class="police-list">
            <ul>
              <li
                v-for="(item, index) in policeAnalysisList"
                :key="index"
                :class="{ bg: index % 2 === 1 }"
              >
                <div class="police-info">
                  <p><b>{{ item.department }}</b><br>{{ item.description }}</p>
                  <p class="date">{{ item.date }}</p>
                </div>
              </li>
            </ul>
          </div>
        </div>

        <!-- 七日数据分析 -->
        <div class="seven-day-analysis">
          <h2 class="section-title">矛盾纠纷七日数据分析</h2>
          <div class="chart-header">
            <p>状态：已调节</p>
            <p>时间：2018-06-14</p>
          </div>
          <div ref="arightboxbottRef" class="chart-content"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.analysis-container {
  width: 100%;
  height: 100vh;
  background: #00065b url('./003/img/bg.jpg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  color: #fff;
  font-family: '微软雅黑', MicrosoftYahei, sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.top-nav {
  height: 9%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;

  .nav-left, .nav-right {
    ul {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        margin: 0 10px;
        cursor: pointer;

        &.active a {
          color: #00ff00;
        }

        a {
          color: #fff;
          text-decoration: none;
          padding: 5px 10px;

          &:hover {
            color: #00ff00;
          }
        }
      }
    }
  }

  .title {
    color: #fff;
    font-size: 24px;
    margin: 0;
    text-align: center;
  }
}

.main-content {
  height: calc(100% - 9%);
  display: flex;
}

.left-section {
  width: 18%;
  padding-left: 2.2%;
  text-align: center;

  .stats-box, .chart-box {
    margin-bottom: 20px;
    background: rgba(1, 202, 217, 0.1);
    border-radius: 5px;
    padding: 15px;
  }

  .section-title {
    color: #fff;
    font-size: 16px;
    margin-bottom: 10px;
    text-align: left;
  }

  .stats-header, .chart-header {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 15px;
  }

  .stats-numbers {
    display: flex;
    justify-content: space-between;

    .stat-item {
      text-align: center;
      flex: 1;

      p {
        font-size: 12px;
        margin-bottom: 5px;
      }

      h3 {
        font-size: 24px;
        margin: 5px 0;

        &.number-orange { color: #eeb1fd; }
        &.number-blue { color: #24c9ff; }
        &.number-yellow { color: #ffff00; }
        &.number-green { color: #11e2dd; }
      }

      .trend {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.6);
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          margin: 0 2px;
        }
      }
    }
  }

  .chart-content {
    width: 100%;
    height: 200px;
  }
}

.middle-section {
  width: 69%;
  padding: 0 10px;

  .monitor-box {
    height: 58%;
    background: rgba(1, 202, 217, 0.1);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;

    .monitor-map {
      width: 100%;
      height: calc(100% - 40px);
      background: url('./003/img/hdmap.png') no-repeat center;
      background-size: contain;
      position: relative;

      .camera-icon {
        position: absolute;
        width: 20px;
        height: 20px;
        background: url('./003/img/camera_l.png') no-repeat center;
        background-size: contain;
        cursor: pointer;

        &:hover {
          transform: scale(1.2);
        }
      }
    }
  }

  .bottom-charts {
    height: 31%;
    display: flex;
    gap: 20px;

    .chart-left, .chart-right {
      flex: 1;
      background: rgba(1, 202, 217, 0.1);
      border-radius: 5px;
      padding: 15px;

      .chart-content {
        width: 100%;
        height: calc(100% - 40px);
      }
    }
  }
}

.right-section {
  width: 25%;
  padding-left: 10px;

  .police-analysis, .seven-day-analysis {
    background: rgba(1, 202, 217, 0.1);
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
  }

  .police-analysis {
    height: 60%;

    .police-list {
      height: calc(100% - 60px);
      overflow-y: auto;

      ul {
        list-style: none;
        margin: 0;
        padding: 0;

        li {
          padding: 10px;
          margin-bottom: 5px;
          border-radius: 3px;

          &.bg {
            background: rgba(255, 255, 255, 0.05);
          }

          .police-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;

            p {
              margin: 0;
              font-size: 12px;
              line-height: 1.4;

              b {
                color: #00ff00;
                font-weight: bold;
              }

              &.date {
                color: rgba(255, 255, 255, 0.7);
                min-width: 80px;
                text-align: right;
                padding-top: 17px;
              }
            }
          }
        }
      }
    }
  }

  .seven-day-analysis {
    height: 31%;

    .chart-content {
      width: 100%;
      height: calc(100% - 60px);
    }
  }
}

/* 滚动条样式 */
.police-list::-webkit-scrollbar {
  width: 6px;
}

.police-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.police-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(255, 255, 255, 0.5);
  }
}
</style>
