<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import http from "@/axios/http"
import MonacoEditor from 'monaco-editor-vue3'
import { ElMessage } from 'element-plus'


const { t } = useI18n()
const loading = ref(false)
const codeContent = ref('')
const language = ref('python')
const output = ref('')
const isRunning = ref(false)








// 支持的语言选项
const languages = [
  { value: 'python', label: 'Python' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'java', label: 'Java' },
  { value: 'sql', label: 'SQL' }
]


// 编辑器选项
const editorOptions = {
  theme: 'vs-white',
  automaticLayout: true,
  minimap: {
    enabled: true
  },
  scrollBeyondLastLine: false,
  fontSize: 14,
  tabSize: 2,
  renderLineHighlight: 'all', // 高亮当前行
  highlightActiveIndentGuide: true, // 高亮活动缩进指南
  bracketPairColorization: { // 括号对着色
    enabled: true
  }
}




// 编辑器内容变化事件
const handleCodeChange = (value) => {
  codeContent.value = value
}


// 切换编程语言
const changeLanguage = () => {
    // monaco-editor-vue3 会自动处理语言切换
    // 这里可以添加额外的逻辑，如果需要的话
}

// 运行代码
const runCode = async () => {
  if (!codeContent.value.trim()) {
    ElMessage.warning('请先编写代码')
    return
  }

  try {
    isRunning.value = true
    output.value = '正在执行...'
    
    const response = await http.post('/api/run-code', {
      code: codeContent.value,
      language: language.value
    })
    
    output.value = response.data.output || '执行成功，无输出'
  } catch (error) {
    output.value = `执行错误: ${error.message || '未知错误'}`
    ElMessage.error('代码执行失败')
  } finally {
    isRunning.value = false
  }
}



// 日志分类器配置
const classifierForm = reactive({
  name: '',
  description: '',
  type: 'regex',
  isActive: true,
  patterns: [{ field: '', pattern: '' }]
})



// 添加新的匹配规则
const addPattern = () => {
  classifierForm.patterns.push({ field: '', pattern: '' })
}

// 删除匹配规则
const removePattern = (index) => {
  classifierForm.patterns.splice(index, 1)
}

// 分类器类型选项
const classifierTypes = [
  { value: 'regex', label: '正则表达式' },
  { value: 'keyword', label: '关键词' },
  { value: 'ml', label: '机器学习' }
]

// 保存分类器配置
const saveClassifier = async () => {
  try {
    loading.value = true
    await http.post('/api/save-classifier', {
      ...classifierForm,
      code: codeContent.value,
      language: language.value
    })
    ElMessage.success('分类器保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

</script>

<template>
  <div class="split-container">
    <!-- 左侧：日志分类器配置 -->
    <div class="classifier-config">
      <h2>{{ t('日志分类器配置') }}</h2>
      
      <el-form :model="classifierForm" label-width="100px">
        <el-form-item :label="t('分类器名称')">
          <el-input v-model="classifierForm.name" />
        </el-form-item>
        
        <el-form-item :label="t('描述')">
          <el-input v-model="classifierForm.description" type="textarea" rows="3" />
        </el-form-item>
        
        <el-form-item :label="t('分类器类型')">
          <el-select v-model="classifierForm.type" style="width: 100%">
            <el-option v-for="item in classifierTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        
        <!-- 匹配规则配置 -->
        <el-form-item :label="t('检测识别规则')">
          <div v-for="(item, index) in classifierForm.patterns" :key="index" class="pattern-item">
            <el-input v-model="item.field" placeholder="字段" class="pattern-field" />
            <span class="pattern-separator">:</span>
            <el-input v-model="item.pattern" placeholder="匹配规则" class="pattern-rule" />
            <el-button type="danger" icon="Delete" circle @click="removePattern(index)" class="pattern-delete" />
          </div>
          <el-button type="primary" plain @click="addPattern" class="add-pattern-btn">
            <el-icon><Plus /></el-icon> {{ t('添加规则') }}
          </el-button>
        </el-form-item>
        
        <el-form-item :label="t('是否启用')">
          <el-switch v-model="classifierForm.isActive" />
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="saveClassifier" :loading="loading">{{ t('保存分类器') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 右侧：代码编辑器 -->
    <div class="code-editor-container">
      <div class="editor-header">
        <h2>{{ t('数据加工（SLS DSL）') }}</h2>
        <div class="editor-controls">
          <el-select v-model="language" @change="changeLanguage" placeholder="选择语言">
            <el-option v-for="item in languages" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-button type="primary" @click="runCode" :loading="isRunning">{{ t('运行') }}</el-button>
        </div>
      </div>
      
      <div class="editor-main">
        <MonacoEditor
          v-model:value="codeContent"
          :language="language"
          :options="editorOptions"
          @change="handleCodeChange"
          class="monaco-editor"
        />
        <div class="output-panel">
          <div class="output-header">{{ t('输出结果') }}</div>
          <pre class="output-content">{{ output }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.split-container {
  display: flex;
  height: 100%;
  gap: 20px;
  
  .classifier-config {
    width: 40%;
    padding: 15px;
    border-right: 1px solid #ddd;
    overflow-y: auto;
    
    .pattern-item {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
      .pattern-field {
        width: 30%;
      }
      
      .pattern-separator {
        margin: 0 8px;
        font-weight: bold;
      }
      
      .pattern-rule {
        flex: 1;
      }
      
      .pattern-delete {
        margin-left: 8px;
      }
    }
    
    .add-pattern-btn {
      width: 50%;
      margin-top: 5px;
    }
  }
  
  .code-editor-container {
    width: 60%;
    display: flex;
    flex-direction: column;
    
    .editor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      
      .editor-controls {
        display: flex;
        gap: 10px;
      }
    }
    
    .editor-main {
      display: flex;
      flex-direction: column;
      flex: 1;
      
      .monaco-editor {
        height: 70%;
        border: 1px solid #ccc;
      }
      
      .output-panel {
        height: 30%;
        background: #1e1e1e;
        color: #fff;
        
        .output-header {
          padding: 5px 10px;
          background: #333;
          font-weight: bold;
        }
        
        .output-content {
          padding: 10px;
          overflow: auto;
          height: calc(100% - 30px);
          margin: 0;

          /* 允许 v-html 内容的样式生效 */
          :deep(code) {
            font-family: 'Consolas', 'Monaco', monospace;
          }
        }
      }
    }
  }
}
</style>