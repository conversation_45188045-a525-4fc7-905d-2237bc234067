<script setup lang="ts">

  import { ref, reactive ,getCurrentInstance,inject,onMounted,computed,onUnmounted} from 'vue'
  import * as echarts from "echarts";
  import { useI18n } from '@/hooks/web/useI18n'
  //图表
  import {trendsCht,lineCht,worldCht,planeWorld,attackpieCht,attacklineCht,attackSource,detectCht,attactServe} from "@/components/public/charts"
  import {mapChart} from "@/components/public/chinaMap"

  import {writeCurrentDate} from "@/components/public/model"
  const parent = getCurrentInstance();

  const { t } = useI18n()
  const activeName = inject('activeName')
  const loading = ref(false)
  const countryDianList = ref([
      { name: '美国', value: [-95.712891, 37.09024] },
      { name: '巴西', value: [-47.929167, -15.789874] },
      { name: '中国', value: [116.069093,39.918255] },
    { name: '俄罗斯', value: [37.691474,56.358984] },
    { name: '日本', value: [139.822303,36.948753] },
  ])
  const echartsRef = ref(null);
  const echartsRef1 = ref(null);
  const echartsRef2 = ref(null);
  const echartsRef3 = ref(null);
  const echartsRef4 = ref(null);
  const echartsRef5 = ref(null);
  const echartsRef6 = ref(null);

  const generateRandomNumbers=(count, min, max)=> {
      const randomNumbers = [];
      for (let i = 0; i < count; i++) {
          randomNumbers.push(Math.floor(Math.random() * (max - min + 1)) + min);
      }
      return randomNumbers;
  }
  const pirChtData = ()=>{
      let dateArr = [];
      for (let i = 1; i <= 30; i++) {
          let num = i;
          if(num<10){
              num = "0"+i;
          }
          dateArr.push("2024-06-"+num);
      }
      echartsRef.value = trendsCht("chart1",dateArr,generateRandomNumbers(30, 0, 30),generateRandomNumbers(30, 0, 30),generateRandomNumbers(30, 0, 30),generateRandomNumbers(30, 0, 30));
      echartsRef1.value = detectCht("chart2",1,[{name:t("table.susperConnection"),value:20},{name:t("table.Abnormalbehavior"),value:33},{name:t("table.bruteForce"),value:121},{name:t("table.exploitation"),value:76}]);
      echartsRef2.value = attacklineCht("chart3",["***************","**************","************","************"],[331,33,53,64],"被攻击次数");
      echartsRef3.value = attackSource("chart4",["************","***************","**************","*************"],[45,346,231,12])
      echartsRef4.value = detectCht("chart5",2,[{name:"***************",value:34},{name:"**************",value:434},{name:"**************",value:55},{name:"*************",value:113}])
      echartsRef5.value = attactServe("chart6",["ftp","ssh","sip","imap","pop3","postgresql","telnet","rdp","adb","http"],[6,7,35,23,2,21,3,35,12,19]);

      //mapChart("chinaMap",[],1);
      let chinaList = [[-110.460272,37.866467,"洛杉矶"],[60.855356,62.842375,"秋明州"],[-107.222348,37.045136,"克罗大多州"],[78.663939,63.70398,"图拉州"],[116.415192,39.978879,"北京"],[37.601172,55.775109,"莫斯科"],[38.189886,55.483669,"莫斯科"],
          [-115.206775,36.237102,"拉斯维加斯"],[-115.390748,36.71258,"拉斯维加斯"],[135.778347,35.084763,"京都"],[138.234386,36.664424,"长野"],[116.405994,39.748488,"北京"],[96.104576,19.817111,"内比都"],[100.667106,13.902979,"曼谷"],[102.709206,18.347819,"万象"],
          [119.988867,30.275513,"杭州"],[120.099251,29.73106,"杭州"]];

      let lineData = [
          {coords: [[-110.460272,37.866467],[60.855356,62.842375],['洛杉矶',"秋明州",34]]},
          {coords: [[-107.222348,37.045136],[78.663939,63.70398],['克罗大多州',"图拉州",341]]},
          {coords: [[116.549722,40.208049],[-112.128677,42.996827],['北京',"洛杉矶",21]]},
          {coords: [[-110.460272,37.866467],[116.415192,39.978879],['北京',"克罗大多州",565]]},
          {coords: [[60.855356,62.842375],[116.415192,39.978879],['图拉州',"北京",32]]},
          {coords: [[37.601172,55.775109],[-115.206775,36.237102],['莫斯科',"拉斯维加斯",876]]},
          {coords: [[38.189886,55.483669],[-115.390748,36.71258],['莫斯科',"拉斯维加斯",33]]},
          {coords: [[116.415192,39.978879],[-115.206775,36.237102],['北京',"拉斯维加斯",8]]},
          {coords: [[-115.390748,36.71258],[116.415192,39.978879],['拉斯维加斯',"北京",3431]]},
          {coords: [[116.405994,39.748488],[135.778347,35.084763],['北京',"京都",384]]},
          {coords: [[138.234386,36.664424],[116.405994,39.748488],['长野',"北京",96]]},
          {coords: [[96.104576,19.817111],[116.415192,39.978879],['内比都',"北京",33]]},
          {coords: [[100.667106,13.902979],[116.415192,39.978879],['曼谷',"北京",32]]},
          {coords: [[102.709206],[116.415192,39.978879],['万象',"北京",556]]},
          {coords: [[119.988867,30.275513],[-112.128677,42.996827],['杭州',"洛杉矶",21]]},
          {coords: [[120.099251,29.73106],[116.415192,39.978879],['杭州',"克罗大多州",565]]},
      ]
      chinaList.forEach((item)=>{
          countryDianList.value.push({value:item})
      })
      echartsRef6.value = planeWorld("chinaMap",countryDianList.value,lineData);
  };
  let assetNumList = ref([
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:25:30",
      value:"可疑连接#尝试连接TCP端口 61.132.163.68 : 37638→202.102.213.68 : 80"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:24:55",
      value:"可疑连接#通过Ping命令探测主机是否在线 219.141.136.10→219.141.140.10"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:21:35",
      value:"可疑连接#通过Ping命令探测主机是否在线 61.128.192.68→61.128.128.68"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:12:53",
      value:"可疑连接#尝试连接TCP端口 218.85.152.99 : 36937→218.85.157.99 : 80"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:12:53",
      value:"可疑连接#通过SYN方式尝试连接TCP端口 202.100.64.68 : 36937→61.178.0.93 : 80"
    },
    {
      name:"本地（srv.dms.tech)：2024-04-30 15:04:09",
      value:"可疑连接#尝试连接UDP端口 202.96.128.86 : 57138→202.96.128.166 : 27856"
    },
    {
      name:"本地（srv.dms.tech)：2024-04-30 12:07:38",
      value:"可疑连接#尝试连接TCP端口 202.96.134.33 : 63382→202.96.128.68 : 514"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:25:30",
      value:"可疑连接#尝试连接TCP端口 202.103.225.68 : 37638→202.103.224.68 : 80"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:24:55",
      value:"可疑连接#通过Ping命令探测主机是否在线 202.98.192.67→202.98.198.167"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:21:35",
      value:"可疑连接#通过Ping命令探测主机是否在线 222.88.88.88→222.85.85.85"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:12:53",
      value:"可疑连接#尝试连接TCP端口 219.147.198.230 : 36937→219.147.198.242 : 80"
    },
    {
      name:"本地（srv.dms.tech)：2024-05-10 12:12:53",
      value:"可疑连接#通过SYN方式尝试连接TCP端口 202.103.24.68 : 36937→202.103.0.68: 80"
    },
    {
      name:"本地（srv.dms.tech)：2024-04-30 15:04:09",
      value:"可疑连接#尝试连接UDP端口 218.2.2.2 : 57138→218.4.4.4 : 27856"
    },
    {
      name:"本地（srv.dms.tech)：2024-04-30 12:07:38",
      value:"可疑连接#尝试连接TCP端口 61.147.37.1 : 63382→218.2.135.1 : 514"
    }
  ])
  const defaultOption = computed(() => {
      return {
        step: 5, // 数值越大速度滚动越快
        limitMoveNum: 5, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 200, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
      waitTime: 5000, // 单步运动停止的时间(默认值1000ms)
    };
  })
  let timeData = ref();

  onMounted(async () => {
    pirChtData();
    setInterval(()=>{
      timeData.value = writeCurrentDate()
    },1000)
  });

  const disposeCharts=()=>{
    chartDis([echartsRef1.value,echartsRef2.value,echartsRef3.value,echartsRef4.value,echartsRef5.value,echartsRef.value]);
  };
  const chartDis =(echartsRefList)=>{
    echartsRefList.forEach((item)=>{
      if (item) {
        window.removeEventListener('resize', item.resize);//销毁图表监听事件
        item.dispose(); // 清理图表实例
        item = null;
      }
    });
  };
  onUnmounted(() => {
    //disposeCharts();
  });
</script>

<template>
  <ElRow :gutter="20" justify="space-between" id="networkScreen" class="screenBackSty">
    <div @click="activeName = ''" style="position: absolute;top:35px;right:30px;color: #fff;font-size: 20px;z-index: 99;cursor: pointer;">
      <el-icon><HomeFilled /></el-icon>
    </div>
    <div class="headTitleSty">
      {{t("viewContent.situationAttacksReal")}}
      <div class="timeSty">{{timeData}}</div>
      <div class="headGifSty"></div>
    </div>
    <div  style="width: 100%;height: 90%;justify-content: space-between;display: flex;">
      <ElCol :xl="5" :lg="5" :md="24" :sm="24" :xs="24">
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div class="screenTitle">{{t("viewContent.attackTrends")}}</div>
          <div id="chart1" class="chartSty"></div>
        </ElCol>
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div class="screenTitle">{{t("logRetrieval.AttackType")}}</div>
          <div id="chart2" class="chartSty"></div>
        </ElCol>
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div class="screenTitle">{{t("logRetrieval.AttackedServices")}}</div>
          <div id="chart6" class="chartSty"></div>
        </ElCol>
      </ElCol>
      <ElCol :xl="14" :lg="14" :md="24" :sm="24" :xs="24" class="screenCen">
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div id="chinaMap" style="width:100%;height: 100%;"></div>
        </ElCol>
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div class="screenTitle screenTitleLong">{{t("logRetrieval.Realtimeattacks")}}</div>
          <vue3-seamless-scroll :list="assetNumList" :class-option="defaultOption" class="seamless-warp" :limitScrollNum=5 :step=.2>
            <ul style="width: 90%;margin: 0 auto;">
              <li v-for="(item,index) in assetNumList">
                <div>
                  <p>
                    <span style="margin-right: 5px">{{item.name}}</span>
                    <el-icon><WarningFilled /></el-icon>
                    <span style="margin-left: 5px">{{item.value}}</span>
                  </p>
                </div>
              </li>
            </ul>
          </vue3-seamless-scroll>
        </ElCol>
      </ElCol>
      <ElCol :xl="5" :lg="5" :md="24" :sm="24" :xs="24">
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div class="screenTitle">{{t("logRetrieval.Alarmranking")}}</div>
          <div id="chart3" class="chartSty"></div>
        </ElCol>
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div class="screenTitle">{{t("logRetrieval.attackSourceIp")}}</div>
          <div id="chart4" class="chartSty"></div>
        </ElCol>
        <ElCol :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
          <div class="screenTitle">{{t("logRetrieval.Detectionbehavior")}}</div>
          <div id="chart5" class="chartSty"></div>
        </ElCol>
      </ElCol>
    </div>

  </ElRow>
</template>
<style lang="less">
  @font-face {
    font-family: 'MyFont';
    src: url('../../../../../assets/svgs/Digital-7Mono.TTF') format('truetype');
  }

  #networkScreen{
    background: url("../../../../../assets/imgs/dataScreen/networkSecurity.png");
    position: fixed;
    left: 0;
    top:0;
    bottom:0;
    right: 0;
    z-index: 999;
    .headTitleSty{
      width: 100%;
      height: 10%;
      text-align: center;
      line-height: 80px;
      font-weight: bold;
      letter-spacing: 5px;
      font-size: 35px;
      background:linear-gradient(to bottom, #8FCCE9, #fff) ;
      -webkit-background-clip: text;
      background-clip: test;
      color: transparent;
      position: relative;
      .headGifSty{
        width: 100%;
        height: 100%;
        position: absolute;
        top:0;
        left:0;
        background: url("../../../../../assets/imgs/dataScreen/title.png") no-repeat;
        background-size: 100%;
        z-index: 2;
      }
      .timeSty{
        position: absolute;
        color: #fff;
        font-size: 18px;
        top:35px;
        letter-spacing: 0;
        left: 30px;
        font-family: 'MyFont', sans-serif;
      }
    }
    >div{
      .screenCen{
        >div:nth-child(1){
          height: 58%!important;
          #chinaMap{
            width: 100%;
            height: 100%;
          }
        }
        >div:nth-child(2){
          height: 31%!important;
        }
      }
      >.el-col{
        height: 100%;
        >.el-col{
          height: 31%;
          margin-top: 3%;
          .screenTitle{
            width: 100%;
            height: 10%;
            color: #8FCCE9;
            background: url("../../../../../assets/imgs/dataScreen/titleHeadBack.png") no-repeat;
            text-indent: 55px;
            font-size: 16px;
            line-height: 150%;
          }
          .screenTitleLong{
            background: url("../../../../../assets/imgs/dataScreen/langScreenDivBack.png") no-repeat;
          }
          .chartSty{
            width: 100%;
            height: 90%;
          }
        }

      }
    }
    .seamless-warp {
      height: 90%;
      overflow: hidden;
      ul{
        li{
          margin-top: 15px;
          >div{
            >p{
              >span:nth-child(1){
                color: #8FCCE9;
              }
              >i:nth-child(2){
                svg{
                  color:#fff;
                }
              }
              >span:nth-child(3){
                color:#fff;
              }
            }
          }
        }
      }
    }
  }
</style>
